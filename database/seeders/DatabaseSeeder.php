<?php

namespace Database\Seeders;

use App\Models\Tenant;
use App\Models\User;
use App\Models\Stream;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create a demo tenant
        $tenant = Tenant::create([
            'domain' => 'localhost',
            'name' => 'Demo Company',
            'theme_config' => [
                'branding' => [
                    'logo_url' => '/logo.svg',
                    'company_name' => 'Demo Company'
                ],
                'colors' => [
                    'primary' => '#0d6efd',
                    'secondary' => '#6c757d',
                    'success' => '#198754',
                    'background' => '#ffffff',
                    'text' => '#212529'
                ]
            ],
            'modules_config' => [
                'modules' => [
                    'chat' => ['enabled' => true, 'moderation' => false],
                    'survey' => ['enabled' => true, 'required' => false],
                    'certificate' => ['enabled' => true, 'auto_generate' => true],
                    'polling' => ['enabled' => true]
                ]
            ],
            'is_active' => true,
        ]);

        // Create a test user for this tenant
        $user = User::create([
            'tenant_id' => $tenant->id,
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);

        // Create a demo stream
        Stream::create([
            'tenant_id' => $tenant->id,
            'title' => 'Welcome to AI Engine',
            'description' => 'An introduction to our AI-enhanced streaming platform',
            'embed_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'start_time' => now()->addHour(),
            'end_time' => now()->addHours(2),
            'is_active' => true,
        ]);
    }
}
