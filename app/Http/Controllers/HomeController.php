<?php

namespace App\Http\Controllers;

use App\Models\Stream;
use App\Services\TenantService;
use Inertia\Inertia;
use Inertia\Response;

class HomeController extends Controller
{
    public function __construct(
        protected TenantService $tenantService
    ) {}

    /**
     * Show the landing page
     */
    public function index(): Response
    {
        $tenant = $this->tenantService->current();

        // Get active streams for this tenant
        $streams = Stream::where('tenant_id', $tenant->id)
            ->where('is_active', true)
            ->orderBy('start_time', 'asc')
            ->get();

        return Inertia::render('Home/Landing', [
            'tenant' => $tenant,
            'themeConfig' => $this->tenantService->getThemeConfig(),
            'modulesConfig' => $this->tenantService->getModulesConfig(),
            'streams' => $streams,
        ]);
    }
}
