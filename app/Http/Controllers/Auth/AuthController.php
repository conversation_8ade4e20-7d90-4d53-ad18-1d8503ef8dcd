<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\TenantService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Inertia\Response;

class AuthController extends Controller
{
    public function __construct(
        protected TenantService $tenantService
    ) {}

    /**
     * Show the login form
     */
    public function showLogin(): Response
    {
        return Inertia::render('Auth/Login', [
            'tenant' => $this->tenantService->current(),
            'themeConfig' => $this->tenantService->getThemeConfig(),
        ]);
    }

    /**
     * Handle login request
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $tenant = $this->tenantService->current();

        // Find user within current tenant
        $user = User::where('tenant_id', $tenant->id)
            ->where('email', $request->email)
            ->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        Auth::login($user, $request->boolean('remember'));

        return redirect()->intended('/stream');
    }

    /**
     * Show the registration form
     */
    public function showRegister(): Response
    {
        return Inertia::render('Auth/Register', [
            'tenant' => $this->tenantService->current(),
            'themeConfig' => $this->tenantService->getThemeConfig(),
        ]);
    }

    /**
     * Handle registration request
     */
    public function register(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $tenant = $this->tenantService->current();

        // Check if email already exists for this tenant
        $existingUser = User::where('tenant_id', $tenant->id)
            ->where('email', $request->email)
            ->first();

        if ($existingUser) {
            throw ValidationException::withMessages([
                'email' => ['The email has already been taken.'],
            ]);
        }

        $user = User::create([
            'tenant_id' => $tenant->id,
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        Auth::login($user);

        return redirect('/thank-you');
    }

    /**
     * Handle logout request
     */
    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/');
    }

    /**
     * Show thank you page
     */
    public function thankYou(): Response
    {
        return Inertia::render('Auth/ThankYou', [
            'tenant' => $this->tenantService->current(),
            'themeConfig' => $this->tenantService->getThemeConfig(),
        ]);
    }
}
