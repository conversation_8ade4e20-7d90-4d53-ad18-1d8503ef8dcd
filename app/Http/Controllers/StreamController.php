<?php

namespace App\Http\Controllers;

use App\Models\Registration;
use App\Models\Stream;
use App\Services\TenantService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class StreamController extends Controller
{
    public function __construct(
        protected TenantService $tenantService
    ) {}

    /**
     * Show the stream dashboard
     */
    public function index(): Response
    {
        $user = Auth::user();
        $tenant = $this->tenantService->current();

        // Get user's registered streams
        $registrations = Registration::with(['stream'])
            ->where('user_id', $user->id)
            ->get();

        // Get active streams for this tenant
        $activeStreams = Stream::where('tenant_id', $tenant->id)
            ->where('is_active', true)
            ->get();

        return Inertia::render('Stream/Dashboard', [
            'tenant' => $tenant,
            'themeConfig' => $this->tenantService->getThemeConfig(),
            'modulesConfig' => $this->tenantService->getModulesConfig(),
            'registrations' => $registrations,
            'activeStreams' => $activeStreams,
            'user' => $user,
        ]);
    }

    /**
     * Show specific stream
     */
    public function show(Stream $stream): Response|RedirectResponse
    {
        $user = Auth::user();

        // Check if user is registered for this stream
        $registration = Registration::where('user_id', $user->id)
            ->where('stream_id', $stream->id)
            ->first();

        if (!$registration) {
            return redirect('/')->with('error', 'You are not registered for this stream.');
        }

        // Mark attendance if stream is live
        if ($stream->isLive() && !$registration->hasAttended()) {
            $registration->markAttended();
        }

        // Load stream relationships
        $stream->load([
            'polls.options',
            'surveys.questions',
            'chatMessages.user',
            'certificates' => function ($query) use ($user) {
                $query->where('user_id', $user->id);
            }
        ]);

        return Inertia::render('Stream/Show', [
            'tenant' => $this->tenantService->current(),
            'themeConfig' => $this->tenantService->getThemeConfig(),
            'modulesConfig' => $this->tenantService->getModulesConfig(),
            'stream' => $stream,
            'registration' => $registration,
            'user' => $user,
        ]);
    }

    /**
     * Register user for a stream
     */
    public function register(Request $request, Stream $stream)
    {
        $user = Auth::user();

        // Check if already registered
        $existingRegistration = Registration::where('user_id', $user->id)
            ->where('stream_id', $stream->id)
            ->first();

        if ($existingRegistration) {
            return back()->with('message', 'You are already registered for this stream.');
        }

        Registration::create([
            'user_id' => $user->id,
            'stream_id' => $stream->id,
            'registered_at' => now(),
        ]);

        return back()->with('message', 'Successfully registered for the stream!');
    }
}
