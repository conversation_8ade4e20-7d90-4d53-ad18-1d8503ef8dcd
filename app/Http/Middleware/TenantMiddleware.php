<?php

namespace App\Http\Middleware;

use App\Services\TenantService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class TenantMiddleware
{
    public function __construct(
        protected TenantService $tenantService
    ) {}

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $domain = $request->getHost();

        $tenant = $this->tenantService->getByDomain($domain);

        if (!$tenant) {
            abort(404, 'Tenant not found');
        }

        // Set tenant context
        $this->tenantService->setCurrent($tenant);

        // Share tenant with views
        view()->share('tenant', $tenant);
        view()->share('themeConfig', $this->tenantService->getThemeConfig());
        view()->share('modulesConfig', $this->tenantService->getModulesConfig());

        return $next($request);
    }
}
