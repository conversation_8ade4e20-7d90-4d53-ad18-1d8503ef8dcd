<?php

namespace App\Services;

use App\Models\Tenant;
use Illuminate\Support\Facades\Cache;

class TenantService
{
    protected ?Tenant $currentTenant = null;

    /**
     * Get the current tenant
     */
    public function current(): ?Tenant
    {
        if ($this->currentTenant) {
            return $this->currentTenant;
        }

        return app('tenant');
    }

    /**
     * Set the current tenant
     */
    public function setCurrent(Tenant $tenant): void
    {
        $this->currentTenant = $tenant;
        app()->instance('tenant', $tenant);
        config(['app.tenant' => $tenant]);
    }

    /**
     * Get tenant by domain with caching
     */
    public function getByDomain(string $domain): ?Tenant
    {
        return Cache::remember("tenant:{$domain}", 3600, function () use ($domain) {
            return Tenant::where('domain', $domain)
                ->where('is_active', true)
                ->first();
        });
    }

    /**
     * Clear tenant cache
     */
    public function clearCache(string $domain): void
    {
        Cache::forget("tenant:{$domain}");
    }

    /**
     * Get tenant theme configuration
     */
    public function getThemeConfig(): array
    {
        $tenant = $this->current();
        
        if (!$tenant || !$tenant->theme_config) {
            return $this->getDefaultThemeConfig();
        }

        return array_merge($this->getDefaultThemeConfig(), $tenant->theme_config);
    }

    /**
     * Get tenant modules configuration
     */
    public function getModulesConfig(): array
    {
        $tenant = $this->current();
        
        if (!$tenant || !$tenant->modules_config) {
            return $this->getDefaultModulesConfig();
        }

        return array_merge($this->getDefaultModulesConfig(), $tenant->modules_config);
    }

    /**
     * Check if a module is enabled for current tenant
     */
    public function isModuleEnabled(string $module): bool
    {
        $config = $this->getModulesConfig();
        return $config['modules'][$module]['enabled'] ?? false;
    }

    /**
     * Get default theme configuration
     */
    protected function getDefaultThemeConfig(): array
    {
        return [
            'branding' => [
                'logo_url' => '/logo.svg',
                'company_name' => 'AI Engine'
            ],
            'colors' => [
                'primary' => '#0d6efd',
                'secondary' => '#6c757d',
                'success' => '#198754',
                'background' => '#ffffff',
                'text' => '#212529'
            ],
            'typography' => [
                'font_family' => 'Inter, sans-serif',
                'heading_weight' => '600',
                'body_weight' => '400'
            ],
            'components' => [
                'button_style' => 'rounded',
                'card_style' => 'elevated',
                'header_variant' => 'minimal'
            ],
            'layout' => [
                'container_width' => '1200px',
                'sidebar_position' => 'left'
            ]
        ];
    }

    /**
     * Get default modules configuration
     */
    protected function getDefaultModulesConfig(): array
    {
        return [
            'modules' => [
                'chat' => ['enabled' => true, 'moderation' => false],
                'survey' => ['enabled' => true, 'required' => false],
                'certificate' => ['enabled' => true, 'auto_generate' => true],
                'polling' => ['enabled' => true]
            ]
        ];
    }
}
