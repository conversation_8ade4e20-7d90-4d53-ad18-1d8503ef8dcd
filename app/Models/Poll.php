<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Poll extends Model
{
    protected $fillable = [
        'stream_id',
        'question',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Boot the model
     */
    protected static function booted(): void
    {
        // Global scope to filter by tenant through stream
        static::addGlobalScope('tenant', function (Builder $builder) {
            if (app()->bound('tenant')) {
                $builder->whereHas('stream', function ($query) {
                    $query->where('tenant_id', app('tenant')->id);
                });
            }
        });
    }

    /**
     * Get the stream that owns the poll
     */
    public function stream(): BelongsTo
    {
        return $this->belongsTo(Stream::class);
    }

    /**
     * Get all options for this poll
     */
    public function options(): Has<PERSON><PERSON>
    {
        return $this->hasMany(PollOption::class);
    }

    /**
     * Get all votes for this poll
     */
    public function votes(): Has<PERSON><PERSON>
    {
        return $this->hasMany(PollVote::class);
    }

    /**
     * Get total vote count
     */
    public function getTotalVotesAttribute(): int
    {
        return $this->votes()->count();
    }

    /**
     * Check if user has voted
     */
    public function hasUserVoted(User $user): bool
    {
        return $this->votes()->where('user_id', $user->id)->exists();
    }
}
