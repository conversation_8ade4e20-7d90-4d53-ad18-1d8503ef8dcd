<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Survey extends Model
{
    protected $fillable = [
        'stream_id',
        'title',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Boot the model
     */
    protected static function booted(): void
    {
        // Global scope to filter by tenant through stream
        static::addGlobalScope('tenant', function (Builder $builder) {
            if (app()->bound('tenant')) {
                $builder->whereHas('stream', function ($query) {
                    $query->where('tenant_id', app('tenant')->id);
                });
            }
        });
    }

    /**
     * Get the stream that owns the survey
     */
    public function stream(): BelongsTo
    {
        return $this->belongsTo(Stream::class);
    }

    /**
     * Get all questions for this survey
     */
    public function questions(): Has<PERSON>any
    {
        return $this->hasMany(SurveyQuestion::class)->orderBy('order');
    }

    /**
     * Get all responses for this survey
     */
    public function responses(): Has<PERSON>any
    {
        return $this->hasMany(SurveyResponse::class);
    }

    /**
     * Check if user has responded
     */
    public function hasUserResponded(User $user): bool
    {
        return $this->responses()->where('user_id', $user->id)->exists();
    }

    /**
     * Get response count
     */
    public function getResponseCountAttribute(): int
    {
        return $this->responses()->count();
    }
}
