<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PollVote extends Model
{
    protected $fillable = [
        'poll_id',
        'poll_option_id',
        'user_id',
    ];

    /**
     * Get the poll that owns the vote
     */
    public function poll(): BelongsTo
    {
        return $this->belongsTo(Poll::class);
    }

    /**
     * Get the poll option that owns the vote
     */
    public function pollOption(): BelongsTo
    {
        return $this->belongsTo(PollOption::class);
    }

    /**
     * Get the user that owns the vote
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
