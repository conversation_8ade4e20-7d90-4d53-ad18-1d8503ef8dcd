<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Tenant extends Model
{
    protected $fillable = [
        'domain',
        'name',
        'theme_config',
        'modules_config',
        'is_active',
    ];

    protected $casts = [
        'theme_config' => 'array',
        'modules_config' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get all users for this tenant
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get all streams for this tenant
     */
    public function streams(): HasMany
    {
        return $this->hasMany(Stream::class);
    }

    /**
     * Check if a module is enabled
     */
    public function isModuleEnabled(string $module): bool
    {
        $config = $this->modules_config ?? [];
        return $config['modules'][$module]['enabled'] ?? false;
    }

    /**
     * Get theme configuration with defaults
     */
    public function getThemeConfigAttribute($value): array
    {
        $default = [
            'branding' => [
                'logo_url' => '/logo.svg',
                'company_name' => $this->name
            ],
            'colors' => [
                'primary' => '#0d6efd',
                'secondary' => '#6c757d',
                'success' => '#198754',
                'background' => '#ffffff',
                'text' => '#212529'
            ],
            'typography' => [
                'font_family' => 'Inter, sans-serif',
                'heading_weight' => '600',
                'body_weight' => '400'
            ],
            'components' => [
                'button_style' => 'rounded',
                'card_style' => 'elevated',
                'header_variant' => 'minimal'
            ],
            'layout' => [
                'container_width' => '1200px',
                'sidebar_position' => 'left'
            ]
        ];

        return array_merge($default, json_decode($value, true) ?? []);
    }
}
