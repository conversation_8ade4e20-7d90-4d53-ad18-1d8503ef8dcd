<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SurveyResponse extends Model
{
    protected $fillable = [
        'survey_id',
        'user_id',
        'responses',
        'submitted_at',
    ];

    protected $casts = [
        'responses' => 'array',
        'submitted_at' => 'datetime',
    ];

    /**
     * Get the survey that owns the response
     */
    public function survey(): BelongsTo
    {
        return $this->belongsTo(Survey::class);
    }

    /**
     * Get the user that owns the response
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get response for a specific question
     */
    public function getResponseForQuestion(int $questionId): mixed
    {
        return $this->responses[$questionId] ?? null;
    }

    /**
     * Set response for a specific question
     */
    public function setResponseForQuestion(int $questionId, mixed $value): void
    {
        $responses = $this->responses ?? [];
        $responses[$questionId] = $value;
        $this->responses = $responses;
    }
}
