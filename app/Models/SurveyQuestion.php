<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SurveyQuestion extends Model
{
    protected $fillable = [
        'survey_id',
        'question_text',
        'question_type',
        'options',
        'is_required',
        'order',
    ];

    protected $casts = [
        'options' => 'array',
        'is_required' => 'boolean',
        'order' => 'integer',
    ];

    /**
     * Get the survey that owns the question
     */
    public function survey(): BelongsTo
    {
        return $this->belongsTo(Survey::class);
    }

    /**
     * Check if question has options
     */
    public function hasOptions(): bool
    {
        return in_array($this->question_type, ['radio', 'checkbox', 'select']);
    }

    /**
     * Get formatted options
     */
    public function getFormattedOptionsAttribute(): array
    {
        if (!$this->hasOptions()) {
            return [];
        }

        return $this->options ?? [];
    }
}
