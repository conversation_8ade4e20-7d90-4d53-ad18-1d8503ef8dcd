<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Registration extends Model
{
    protected $fillable = [
        'user_id',
        'stream_id',
        'registered_at',
        'attended_at',
    ];

    protected $casts = [
        'registered_at' => 'datetime',
        'attended_at' => 'datetime',
    ];

    /**
     * Boot the model
     */
    protected static function booted(): void
    {
        // Global scope to filter by tenant through stream
        static::addGlobalScope('tenant', function (Builder $builder) {
            if (app()->bound('tenant')) {
                $builder->whereHas('stream', function ($query) {
                    $query->where('tenant_id', app('tenant')->id);
                });
            }
        });
    }

    /**
     * Get the user that owns the registration
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the stream that owns the registration
     */
    public function stream(): BelongsTo
    {
        return $this->belongsTo(Stream::class);
    }

    /**
     * Mark attendance
     */
    public function markAttended(): void
    {
        $this->update(['attended_at' => now()]);
    }

    /**
     * Check if user attended
     */
    public function hasAttended(): bool
    {
        return $this->attended_at !== null;
    }
}
