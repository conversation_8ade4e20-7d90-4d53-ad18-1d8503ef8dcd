<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PollOption extends Model
{
    protected $fillable = [
        'poll_id',
        'option_text',
        'vote_count',
    ];

    protected $casts = [
        'vote_count' => 'integer',
    ];

    /**
     * Get the poll that owns the option
     */
    public function poll(): BelongsTo
    {
        return $this->belongsTo(Poll::class);
    }

    /**
     * Get all votes for this option
     */
    public function votes(): HasMany
    {
        return $this->hasMany(PollVote::class);
    }

    /**
     * Increment vote count
     */
    public function incrementVoteCount(): void
    {
        $this->increment('vote_count');
    }

    /**
     * Decrement vote count
     */
    public function decrementVoteCount(): void
    {
        $this->decrement('vote_count');
    }

    /**
     * Get vote percentage
     */
    public function getVotePercentageAttribute(): float
    {
        $totalVotes = $this->poll->total_votes;
        return $totalVotes > 0 ? ($this->vote_count / $totalVotes) * 100 : 0;
    }
}
