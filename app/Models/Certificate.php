<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Certificate extends Model
{
    protected $fillable = [
        'user_id',
        'stream_id',
        'certificate_data',
        'issued_at',
        'download_count',
    ];

    protected $casts = [
        'certificate_data' => 'array',
        'issued_at' => 'datetime',
        'download_count' => 'integer',
    ];

    /**
     * Boot the model
     */
    protected static function booted(): void
    {
        // Global scope to filter by tenant through stream
        static::addGlobalScope('tenant', function (Builder $builder) {
            if (app()->bound('tenant')) {
                $builder->whereHas('stream', function ($query) {
                    $query->where('tenant_id', app('tenant')->id);
                });
            }
        });
    }

    /**
     * Get the user that owns the certificate
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the stream that owns the certificate
     */
    public function stream(): BelongsTo
    {
        return $this->belongsTo(Stream::class);
    }

    /**
     * Increment download count
     */
    public function incrementDownloadCount(): void
    {
        $this->increment('download_count');
    }

    /**
     * Get certificate title
     */
    public function getTitleAttribute(): string
    {
        return $this->certificate_data['title'] ?? 'Certificate of Completion';
    }

    /**
     * Get recipient name
     */
    public function getRecipientNameAttribute(): string
    {
        return $this->certificate_data['recipient_name'] ?? $this->user->name;
    }
}
