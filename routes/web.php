<?php

use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\StreamController;
use Illuminate\Support\Facades\Route;

// Public routes (Guest Access)
Route::get('/', [HomeController::class, 'index'])->name('home');

// Authentication routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
});

Route::get('/thank-you', [AuthController::class, 'thankYou'])->name('thank-you');

// Protected routes (Authenticated Users)
Route::middleware('auth')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

    // Stream routes
    Route::get('/stream', [StreamController::class, 'index'])->name('stream.dashboard');
    Route::get('/stream/{stream}', [StreamController::class, 'show'])->name('stream.show');
    Route::post('/stream/{stream}/register', [StreamController::class, 'register'])->name('stream.register');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
