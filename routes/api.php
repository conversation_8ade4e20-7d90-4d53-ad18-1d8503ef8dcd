<?php

use App\Http\Controllers\Api\ChatController;
use App\Http\Controllers\Api\PollController;
use App\Http\Controllers\Api\SurveyController;
use App\Http\Controllers\Api\CertificateController;
use Illuminate\Support\Facades\Route;

// API routes for real-time features
Route::middleware(['auth:sanctum'])->group(function () {
    
    // Chat API
    Route::prefix('chat')->group(function () {
        Route::post('/send', [ChatController::class, 'send'])->name('api.chat.send');
        Route::get('/messages/{stream}', [ChatController::class, 'messages'])->name('api.chat.messages');
    });

    // Polling API
    Route::prefix('polls')->group(function () {
        Route::post('/{poll}/vote', [PollController::class, 'vote'])->name('api.polls.vote');
        Route::get('/{poll}/results', [PollController::class, 'results'])->name('api.polls.results');
    });

    // Survey API
    Route::prefix('surveys')->group(function () {
        Route::post('/{survey}/submit', [SurveyController::class, 'submit'])->name('api.surveys.submit');
        Route::get('/{survey}/responses', [SurveyController::class, 'responses'])->name('api.surveys.responses');
    });

    // Certificate API
    Route::prefix('certificates')->group(function () {
        Route::get('/{stream}/download', [CertificateController::class, 'download'])->name('api.certificates.download');
        Route::post('/{stream}/generate', [CertificateController::class, 'generate'])->name('api.certificates.generate');
    });
});
